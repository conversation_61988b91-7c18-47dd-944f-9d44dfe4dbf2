#!/usr/bin/env python3
"""
测试管理员充值功能的脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tortoise import Tortoise
from app.settings import settings
from app.controllers.user_credit import user_credit_controller


async def test_admin_recharge():
    """测试管理员充值功能"""
    # 初始化数据库连接
    await Tortoise.init(config=settings.TORTOISE_ORM)
    
    try:
        # 测试用户ID
        test_user_id = 1
        
        print("=== 管理员充值功能测试 ===")
        
        # 1. 获取用户当前积分
        print(f"1. 获取用户 {test_user_id} 的当前积分...")
        user_credit = await user_credit_controller.get_or_create_user_credit(test_user_id)
        print(f"   充值前 - 已消耗: {user_credit.consumed_credits}, 剩余: {user_credit.remaining_credits}")
        
        # 2. 管理员充值
        recharge_amount = 500
        print(f"\n2. 管理员为用户充值 {recharge_amount} 积分...")
        await user_credit_controller.recharge_credits(
            user_id=test_user_id,
            amount=recharge_amount,
            description="管理员手动充值测试"
        )
        
        # 3. 验证充值结果
        user_credit = await user_credit_controller.get_user_credit(test_user_id)
        print(f"   充值后 - 已消耗: {user_credit.consumed_credits}, 剩余: {user_credit.remaining_credits}")
        
        # 4. 测试消耗一些积分
        consume_amount = 100
        print(f"\n3. 测试消耗 {consume_amount} 积分...")
        success = await user_credit_controller.consume_credits(
            user_id=test_user_id,
            amount=consume_amount,
            description="测试消耗积分"
        )
        
        if success:
            user_credit = await user_credit_controller.get_user_credit(test_user_id)
            print(f"   消耗后 - 已消耗: {user_credit.consumed_credits}, 剩余: {user_credit.remaining_credits}")
        else:
            print("   消耗失败，积分不足")
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(test_admin_recharge())
