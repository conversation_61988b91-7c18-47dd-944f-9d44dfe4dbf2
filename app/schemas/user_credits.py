from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class BaseUserCredit(BaseModel):
    total_credits: int = Field(0, description="总积分", example=10000)
    consumed_credits: int = Field(0, description="已消耗积分", example=2500)
    remaining_credits: int = Field(0, description="剩余积分", example=7500)


class UserCreditCreate(BaseUserCredit):
    user_id: int = Field(..., description="用户ID", example=1)


class UserCreditUpdate(BaseModel):
    id: int = Field(..., description="记录ID")
    total_credits: Optional[int] = Field(None, description="总积分")
    consumed_credits: Optional[int] = Field(None, description="已消耗积分")
    remaining_credits: Optional[int] = Field(None, description="剩余积分")

    def update_dict(self):
        return self.model_dump(exclude_unset=True, exclude={"id"})


class UserCreditResponse(BaseUserCredit):
    id: int = Field(..., description="记录ID")
    user_id: int = Field(..., description="用户ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class BaseCreditTransaction(BaseModel):
    transaction_type: str = Field(..., description="交易类型", example="consume")
    amount: int = Field(..., description="积分数量", example=100)
    balance_before: int = Field(..., description="交易前余额", example=1000)
    balance_after: int = Field(..., description="交易后余额", example=900)
    description: str = Field(..., description="交易描述", example="deai接口消耗积分")
    related_record_id: Optional[int] = Field(None, description="关联记录ID")


class CreditTransactionCreate(BaseCreditTransaction):
    user_id: int = Field(..., description="用户ID", example=1)


class CreditTransactionResponse(BaseCreditTransaction):
    id: int = Field(..., description="交易ID")
    user_id: int = Field(..., description="用户ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class CreditTransactionQuery(BaseModel):
    user_id: Optional[int] = Field(None, description="用户ID")
    transaction_type: Optional[str] = Field(None, description="交易类型")
    page: int = Field(1, description="页码", ge=1)
    page_size: int = Field(10, description="每页数量", ge=1, le=100)


class CreditTransactionListResponse(BaseModel):
    items: list[CreditTransactionResponse] = Field(..., description="交易记录列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(..., description="总页数")


class CreditRechargeRequest(BaseModel):
    amount: int = Field(..., description="充值积分数量", example=1000, gt=0)
    description: str = Field("积分充值", description="充值描述", example="管理员充值")
