from typing import Optional
from tortoise.expressions import Q
from tortoise.transactions import atomic

from app.core.crud import CRUDBase
from app.models.admin import UserCredit, CreditTransaction
from app.schemas.user_credits import (
    UserCreditCreate, 
    UserCreditUpdate, 
    CreditTransactionCreate,
    CreditTransactionQuery
)


class UserCreditController(CRUDBase[UserCredit, UserCreditCreate, UserCreditUpdate]):
    def __init__(self):
        super().__init__(model=UserCredit)

    async def get_user_credit(self, user_id: int) -> Optional[UserCredit]:
        """获取用户积分信息"""
        return await self.model.filter(user_id=user_id).first()

    async def get_or_create_user_credit(self, user_id: int) -> UserCredit:
        """获取或创建用户积分记录"""
        user_credit = await self.get_user_credit(user_id)
        if not user_credit:
            # 创建新用户积分记录，默认给予1000积分
            user_credit_data = UserCreditCreate(
                user_id=user_id,
                total_credits=1000,
                consumed_credits=0,
                remaining_credits=1000
            )
            user_credit = await self.create(obj_in=user_credit_data)
        return user_credit

    @atomic()
    async def consume_credits(self, user_id: int, amount: int, description: str, related_record_id: Optional[int] = None) -> bool:
        """消耗积分"""
        user_credit = await self.get_or_create_user_credit(user_id)
        
        # 检查积分是否足够
        if user_credit.remaining_credits < amount:
            return False
        
        # 更新积分
        balance_before = user_credit.remaining_credits
        user_credit.consumed_credits += amount
        user_credit.remaining_credits -= amount
        await user_credit.save()
        
        # 记录交易
        transaction_data = CreditTransactionCreate(
            user_id=user_id,
            transaction_type="consume",
            amount=amount,
            balance_before=balance_before,
            balance_after=user_credit.remaining_credits,
            description=description,
            related_record_id=related_record_id
        )
        await credit_transaction_controller.create_transaction(transaction_data)
        
        return True

    @atomic()
    async def recharge_credits(self, user_id: int, amount: int, description: str = "积分充值") -> UserCredit:
        """充值积分"""
        user_credit = await self.get_or_create_user_credit(user_id)
        
        balance_before = user_credit.remaining_credits
        user_credit.total_credits += amount
        user_credit.remaining_credits += amount
        await user_credit.save()
        
        # 记录交易
        transaction_data = CreditTransactionCreate(
            user_id=user_id,
            transaction_type="recharge",
            amount=amount,
            balance_before=balance_before,
            balance_after=user_credit.remaining_credits,
            description=description
        )
        await credit_transaction_controller.create_transaction(transaction_data)
        
        return user_credit


class CreditTransactionController(CRUDBase[CreditTransaction, CreditTransactionCreate, None]):
    def __init__(self):
        super().__init__(model=CreditTransaction)

    async def create_transaction(self, obj_in: CreditTransactionCreate) -> CreditTransaction:
        """创建交易记录"""
        return await self.create(obj_in=obj_in)

    async def get_user_transactions(self, user_id: int, query: CreditTransactionQuery):
        """获取用户交易记录列表"""
        q = Q(user_id=user_id)
        
        # 添加筛选条件
        if query.transaction_type:
            q &= Q(transaction_type=query.transaction_type)

        # 计算总数
        total = await self.model.filter(q).count()
        
        # 分页查询
        offset = (query.page - 1) * query.page_size
        transactions = await self.model.filter(q).order_by("-created_at").offset(offset).limit(query.page_size)
        
        # 计算总页数
        total_pages = (total + query.page_size - 1) // query.page_size
        
        return {
            "items": transactions,
            "total": total,
            "page": query.page,
            "page_size": query.page_size,
            "total_pages": total_pages
        }


# 创建控制器实例
user_credit_controller = UserCreditController()
credit_transaction_controller = CreditTransactionController()
