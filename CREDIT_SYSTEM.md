# 用户积分系统实现文档

## 概述

为项目添加了用户积分系统，用于记录每个用户的积分消耗和剩余积分。系统会在用户使用deai、rewrite和upload接口时自动扣除相应积分。

## 数据库设计

### 1. UserCredit 表 (用户积分表)
```sql
CREATE TABLE user_credit (
    id BIGINT PRIMARY KEY,
    user_id INT UNIQUE NOT NULL,  -- 用户ID，唯一索引
    consumed_credits BIGINT DEFAULT 0,  -- 已消耗积分
    remaining_credits BIGINT DEFAULT 0,  -- 剩余积分
    created_at DATETIME,
    updated_at DATETIME
);
```

### 2. CreditTransaction 表 (积分交易记录表)
```sql
CREATE TABLE credit_transaction (
    id BIGINT PRIMARY KEY,
    user_id INT NOT NULL,  -- 用户ID
    transaction_type VARCHAR(20) NOT NULL,  -- 交易类型: consume, recharge
    amount BIGINT NOT NULL,  -- 积分数量
    balance_before BIGINT NOT NULL,  -- 交易前余额
    balance_after BIGINT NOT NULL,  -- 交易后余额
    description VARCHAR(255) NOT NULL,  -- 交易描述
    related_record_id INT,  -- 关联记录ID（可选）
    created_at DATETIME,
    updated_at DATETIME
);
```

## 积分消耗规则

### 1. deai接口
- **消耗规则**: 使用 `len(request.text)` 计算字符串长度作为消耗积分
- **描述**: "deai接口消耗积分，处理文本长度: {字符数}字符"

### 2. rewrite接口
- **消耗规则**: 使用 `len(request.text)` 计算字符串长度作为消耗积分
- **描述**: "rewrite接口消耗积分，处理文本长度: {字符数}字符"

### 3. upload接口
- **消耗规则**: 使用第三方API返回值中的 `cost` 字段作为消耗积分
- **API返回格式**: `{'status': 'processing', 'message': 'Document is being processed.', 'user_doc_id': '625f11d4-98fd-4609-8df4-fab7d9ed6086', 'cost': 6684}`
- **描述**: "upload接口消耗积分，文件: {文件名}，cost: {cost值}"

## API接口

### 1. 获取用户积分信息
```
GET /api/v1/user_credits/info
```
返回当前用户的积分信息（已消耗积分和剩余积分）

### 2. 充值积分
```
POST /api/v1/user_credits/recharge
{
    "amount": 1000,
    "description": "管理员充值"
}
```

### 3. 获取积分交易记录
```
GET /api/v1/user_credits/transactions?page=1&page_size=10&transaction_type=consume
```

## 前端集成

### 1. 积分显示
在humanizer页面顶部添加了积分信息卡片，显示：
- 剩余积分（绿色）
- 已消耗积分（红色）

### 2. 实时更新
- 页面加载时自动获取积分信息
- 成功处理文本后自动刷新积分信息

## 核心逻辑

### 1. 新用户默认积分
新用户首次访问时会自动创建积分记录，默认给予1000积分。

### 2. 积分不足处理
当用户积分不足时，接口会返回400错误，提示"积分不足，请充值后再试"。

### 3. 事务安全
所有积分操作都使用数据库事务确保数据一致性。

## 文件结构

```
app/
├── models/admin.py              # 添加了UserCredit和CreditTransaction模型
├── schemas/user_credits.py      # 积分相关的Pydantic模型
├── controllers/user_credit.py   # 积分业务逻辑控制器
├── api/v1/user_credits/         # 积分相关API接口
└── api/v1/humanizer/humanizer.py # 修改了deai、rewrite、upload接口

web/
├── src/api/index.js             # 添加了积分相关API调用
└── src/views/humanizer/index.vue # 添加了积分显示和实时更新
```

## 使用说明

1. **管理员充值**: 可以通过API为用户充值积分
2. **用户查看**: 用户可以在humanizer页面查看自己的积分情况
3. **自动扣费**: 使用deai、rewrite、upload接口时会自动扣除相应积分
4. **交易记录**: 所有积分变动都会记录在交易表中，便于追踪

## 测试

运行测试脚本验证积分系统：
```bash
python test_credit_system.py
```

该脚本会测试：
- 用户积分创建
- 积分充值
- 积分消耗
- 积分不足处理
