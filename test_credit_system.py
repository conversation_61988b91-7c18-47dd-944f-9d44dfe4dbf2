#!/usr/bin/env python3
"""
测试积分系统的脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tortoise import Tortoise
from app.settings import settings
from app.controllers.user_credit import user_credit_controller


async def test_credit_system():
    """测试积分系统"""
    # 初始化数据库连接
    await Tortoise.init(config=settings.TORTOISE_ORM)
    
    try:
        # 测试用户ID
        test_user_id = 1
        
        print("=== 积分系统测试 ===")
        
        # 1. 获取或创建用户积分
        print(f"1. 获取用户 {test_user_id} 的积分信息...")
        user_credit = await user_credit_controller.get_or_create_user_credit(test_user_id)
        print(f"   总积分: {user_credit.total_credits}")
        print(f"   已消耗: {user_credit.consumed_credits}")
        print(f"   剩余: {user_credit.remaining_credits}")
        
        # 2. 测试充值
        print(f"\n2. 为用户充值 500 积分...")
        await user_credit_controller.recharge_credits(test_user_id, 500, "测试充值")
        user_credit = await user_credit_controller.get_user_credit(test_user_id)
        print(f"   充值后剩余积分: {user_credit.remaining_credits}")
        
        # 3. 测试消耗积分
        print(f"\n3. 测试消耗 100 积分...")
        success = await user_credit_controller.consume_credits(
            test_user_id, 100, "测试消耗积分"
        )
        if success:
            user_credit = await user_credit_controller.get_user_credit(test_user_id)
            print(f"   消耗成功，剩余积分: {user_credit.remaining_credits}")
        else:
            print("   消耗失败，积分不足")
        
        # 4. 测试积分不足的情况
        print(f"\n4. 测试消耗超额积分...")
        success = await user_credit_controller.consume_credits(
            test_user_id, 999999, "测试超额消耗"
        )
        if success:
            print("   消耗成功")
        else:
            print("   消耗失败，积分不足（预期结果）")
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(test_credit_system())
